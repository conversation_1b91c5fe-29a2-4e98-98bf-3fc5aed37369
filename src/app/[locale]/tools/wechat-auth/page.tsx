"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { useTranslations, useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';
import { SITE_URL } from "@/lib/constants";

export default function WechatAuthPage() {
  const t = useTranslations('pages.tools.tools.wechatAuth');
  const tSite = useTranslations('site');
  const locale = useLocale();

  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  // 状态管理
  const [authType, setAuthType] = useState('wechat'); // wechat 或 work_wechat
  const [appId, setAppId] = useState('');
  const [redirectUri, setRedirectUri] = useState('');
  const [responseType, setResponseType] = useState('code');
  const [scope, setScope] = useState('snsapi_userinfo');
  const [state, setState] = useState('');
  const [agentId, setAgentId] = useState(''); // 企业微信专用
  const [generatedUrl, setGeneratedUrl] = useState('');
  const [showAdvanced, setShowAdvanced] = useState(false);

  // 微信公众号授权链接生成
  const generateWechatAuthUrl = useCallback(() => {
    if (!appId || !redirectUri) {
      setGeneratedUrl('');
      return;
    }

    const baseUrl = 'https://open.weixin.qq.com/connect/oauth2/authorize';
    const params = new URLSearchParams({
      appid: appId,
      redirect_uri: encodeURIComponent(redirectUri),
      response_type: responseType,
      scope: scope,
      ...(state && { state: state })
    });

    const url = `${baseUrl}?${params.toString()}#wechat_redirect`;
    setGeneratedUrl(url);
  }, [appId, redirectUri, responseType, scope, state]);

  // 企业微信授权链接生成
  const generateWorkWechatAuthUrl = useCallback(() => {
    if (!appId || !redirectUri) {
      setGeneratedUrl('');
      return;
    }

    const baseUrl = 'https://open.weixin.qq.com/connect/oauth2/authorize';
    const params = new URLSearchParams({
      appid: appId,
      redirect_uri: encodeURIComponent(redirectUri),
      response_type: responseType,
      scope: scope,
      ...(agentId && { agentid: agentId }),
      ...(state && { state: state })
    });

    const url = `${baseUrl}?${params.toString()}#wechat_redirect`;
    setGeneratedUrl(url);
  }, [appId, redirectUri, responseType, scope, agentId, state]);

  // 授权类型切换时更新默认scope
  useEffect(() => {
    if (authType === 'wechat') {
      setScope('snsapi_userinfo');
    } else {
      setScope('snsapi_base');
    }
  }, [authType]);

  // 自动生成链接
  useEffect(() => {
    if (authType === 'wechat') {
      generateWechatAuthUrl();
    } else {
      generateWorkWechatAuthUrl();
    }
  }, [authType, appId, redirectUri, responseType, scope, state, agentId, generateWechatAuthUrl, generateWorkWechatAuthUrl]);

  // 复制到剪贴板
  const handleCopyUrl = async () => {
    if (!generatedUrl) return;
    
    try {
      await navigator.clipboard.writeText(generatedUrl);
      alert(t('messages.copySuccess'));
    } catch {
      alert(t('messages.copyFailed'));
    }
  };

  // 清空表单
  const handleClear = () => {
    setAppId('');
    setRedirectUri('');
    setResponseType('code');
    setScope(authType === 'wechat' ? 'snsapi_userinfo' : 'snsapi_base');
    setState('');
    setAgentId('');
    setGeneratedUrl('');
  };

  // 预设示例
  const handleLoadExample = () => {
    if (authType === 'wechat') {
      setAppId('wx1234567890abcdef');
      setRedirectUri('https://your-domain.com/callback');
      setScope('snsapi_userinfo');
      setState('STATE123');
    } else {
      setAppId('ww1234567890abcdef');
      setRedirectUri('https://your-domain.com/callback');
      setScope('snsapi_base');
      setAgentId('1000001');
      setState('STATE123');
    }
  };

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": t('meta.title'),
    "description": t('meta.description'),
    "url": `${SITE_URL}${getLocalizedHref('/tools/wechat-auth')}`,
    "applicationCategory": "DeveloperApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "CNY"
    },
    "featureList": [
      t('features.wechat'),
      t('features.workWechat'),
      t('features.customization'),
      t('features.preview')
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container-custom py-12">
          {/* 面包屑导航 */}
          <nav className="flex mb-8" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                  <svg className="w-3 h-3 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                  </svg>
                  {t('breadcrumb.home')}
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                    {t('breadcrumb.tools')}
                  </Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 dark:text-gray-400">{t('breadcrumb.current')}</span>
                </div>
              </li>
            </ol>
          </nav>

          {/* 页面标题 */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              {t('title')}
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              {t('description')}
            </p>
          </div>

          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 左侧配置面板 */}
              <div className="space-y-6">
                {/* 授权类型选择 */}
                <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-slate-700">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    {t('authType.title')}
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <button
                      onClick={() => setAuthType('wechat')}
                      className={`p-4 rounded-lg border-2 transition-all ${
                        authType === 'wechat'
                          ? 'border-green-500 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                          : 'border-gray-200 dark:border-slate-600 hover:border-green-300 dark:hover:border-green-600'
                      }`}
                    >
                      <div className="text-2xl mb-2">💬</div>
                      <div className="font-medium">{t('authType.wechat')}</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        {t('authType.wechatDesc')}
                      </div>
                    </button>
                    <button
                      onClick={() => setAuthType('work_wechat')}
                      className={`p-4 rounded-lg border-2 transition-all ${
                        authType === 'work_wechat'
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                          : 'border-gray-200 dark:border-slate-600 hover:border-blue-300 dark:hover:border-blue-600'
                      }`}
                    >
                      <div className="text-2xl mb-2">🏢</div>
                      <div className="font-medium">{t('authType.workWechat')}</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        {t('authType.workWechatDesc')}
                      </div>
                    </button>
                  </div>
                </div>

                {/* 基本配置 */}
                <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-slate-700">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {t('config.title')}
                    </h3>
                    <button
                      onClick={handleLoadExample}
                      className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
                    >
                      {t('config.loadExample')}
                    </button>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {authType === 'wechat' ? t('config.appId') : t('config.corpId')} *
                      </label>
                      <input
                        type="text"
                        value={appId}
                        onChange={(e) => setAppId(e.target.value)}
                        placeholder={authType === 'wechat' ? 'wx1234567890abcdef' : 'ww1234567890abcdef'}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('config.redirectUri')} *
                      </label>
                      <input
                        type="url"
                        value={redirectUri}
                        onChange={(e) => setRedirectUri(e.target.value)}
                        placeholder="https://your-domain.com/callback"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                      />
                    </div>

                    {authType === 'work_wechat' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          {t('config.agentId')}
                        </label>
                        <input
                          type="text"
                          value={agentId}
                          onChange={(e) => setAgentId(e.target.value)}
                          placeholder="1000001"
                          className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                        />
                      </div>
                    )}

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('config.scope')}
                      </label>
                      <select
                        value={scope}
                        onChange={(e) => setScope(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                      >
                        {authType === 'wechat' ? (
                          <>
                            <option value="snsapi_base">{t('config.scopes.snsapi_base')}</option>
                            <option value="snsapi_userinfo">{t('config.scopes.snsapi_userinfo')}</option>
                          </>
                        ) : (
                          <>
                            <option value="snsapi_base">{t('config.scopes.snsapi_base')}</option>
                            <option value="snsapi_privateinfo">{t('config.scopes.snsapi_privateinfo')}</option>
                          </>
                        )}
                      </select>
                    </div>

                    {/* 高级设置 */}
                    <div>
                      <button
                        onClick={() => setShowAdvanced(!showAdvanced)}
                        className="flex items-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
                      >
                        <svg className={`w-4 h-4 mr-1 transition-transform ${showAdvanced ? 'rotate-90' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                        {showAdvanced ? t('config.hideAdvanced') : t('config.showAdvanced')}
                      </button>
                    </div>

                    {showAdvanced && (
                      <div className="space-y-4 pt-4 border-t border-gray-200 dark:border-slate-600">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {t('config.responseType')}
                          </label>
                          <select
                            value={responseType}
                            onChange={(e) => setResponseType(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                          >
                            <option value="code">code</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {t('config.state')}
                          </label>
                          <input
                            type="text"
                            value={state}
                            onChange={(e) => setState(e.target.value)}
                            placeholder="STATE123"
                            className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                          />
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {t('config.stateDesc')}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 右侧结果面板 */}
              <div className="space-y-6">
                {/* 生成的授权链接 */}
                <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-slate-700">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    {t('result.title')}
                  </h3>
                  
                  {generatedUrl ? (
                    <div className="space-y-4">
                      <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                        <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {t('result.generatedUrl')}
                        </div>
                        <div className="break-all text-sm font-mono bg-white dark:bg-slate-800 p-3 rounded border">
                          {generatedUrl}
                        </div>
                      </div>
                      
                      <div className="flex gap-3">
                        <button
                          onClick={handleCopyUrl}
                          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                          {t('actions.copy')}
                        </button>
                        <button
                          onClick={handleClear}
                          className="px-4 py-2 border border-gray-300 dark:border-slate-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors"
                        >
                          {t('actions.clear')}
                        </button>
                      </div>
                      
                      <div className="text-center">
                        <a
                          href={generatedUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
                        >
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                          </svg>
                          {t('actions.testUrl')}
                        </a>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      <div className="text-4xl mb-4">🔗</div>
                      <p>{t('result.placeholder')}</p>
                    </div>
                  )}
                </div>

                {/* 使用说明 */}
                <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-slate-700">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    {t('instructions.title')}
                  </h3>
                  <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex items-start">
                      <span className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">1</span>
                      <p>{t('instructions.step1')}</p>
                    </div>
                    <div className="flex items-start">
                      <span className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">2</span>
                      <p>{t('instructions.step2')}</p>
                    </div>
                    <div className="flex items-start">
                      <span className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">3</span>
                      <p>{t('instructions.step3')}</p>
                    </div>
                    <div className="flex items-start">
                      <span className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">4</span>
                      <p>{t('instructions.step4')}</p>
                    </div>
                  </div>
                </div>

                {/* 小贴士 */}
                <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-amber-800 dark:text-amber-200 mb-4">
                    💡 {t('tips.title')}
                  </h3>
                  <div className="space-y-2 text-sm text-amber-700 dark:text-amber-300">
                    {authType === 'wechat' ? (
                      <>
                        <p>• {t('tips.wechat.security')}</p>
                        <p>• {t('tips.wechat.redirect')}</p>
                        <p>• {t('tips.wechat.scope')}</p>
                        <p>• {t('tips.wechat.state')}</p>
                      </>
                    ) : (
                      <>
                        <p>• {t('tips.workWechat.security')}</p>
                        <p>• {t('tips.workWechat.redirect')}</p>
                        <p>• {t('tips.workWechat.scope')}</p>
                        <p>• {t('tips.workWechat.state')}</p>
                        <p>• {t('tips.workWechat.agent')}</p>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}