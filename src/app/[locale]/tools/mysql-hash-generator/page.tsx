"use client";

import { useState } from "react";
import Link from "next/link";
import { useTranslations, useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';
import { SITE_NAME, SITE_URL } from "@/lib/constants";
import crypto from 'crypto';

export default function MySQLHashGeneratorPage() {
  const [password, setPassword] = useState("");
  const [hashedPassword, setHashedPassword] = useState("");
  const [hashType, setHashType] = useState("PASSWORD");
  
  const t = useTranslations('pages.tools.tools.mysqlHashGenerator');
  const tSite = useTranslations('site');
  const locale = useLocale();
  
  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  // MySQL PASSWORD() 函数实现
  const mysqlPassword = (password: string): string => {
    if (!password) return "";
    
    // MySQL PASSWORD() 使用双重SHA1哈希
    const firstHash = crypto.createHash('sha1').update(password, 'utf8').digest();
    const secondHash = crypto.createHash('sha1').update(firstHash).digest('hex');
    
    return '*' + secondHash.toUpperCase();
  };

  // MySQL OLD_PASSWORD() 函数实现（MySQL 4.1之前）
  const mysqlOldPassword = (password: string): string => {
    if (!password) return "";
    
    let hash1 = 1345345333;
    let hash2 = 0x12345671;
    let add = 7;
    
    for (let i = 0; i < password.length; i++) {
      const char = password.charCodeAt(i);
      if (char === 32 || char === 9) continue; // 跳过空格和制表符
      
      hash1 ^= (((hash1 & 63) + add) * char) + (hash1 << 8);
      hash2 += (hash2 << 8) ^ hash1;
      add += char;
    }
    
    hash1 &= 0x7fffffff;
    hash2 &= 0x7fffffff;
    
    return hash1.toString(16).padStart(8, '0') + hash2.toString(16).padStart(8, '0');
  };

  // SHA1哈希
  const sha1Hash = (password: string): string => {
    if (!password) return "";
    return crypto.createHash('sha1').update(password, 'utf8').digest('hex');
  };

  // SHA256哈希
  const sha256Hash = (password: string): string => {
    if (!password) return "";
    return crypto.createHash('sha256').update(password, 'utf8').digest('hex');
  };

  // MD5哈希
  const md5Hash = (password: string): string => {
    if (!password) return "";
    return crypto.createHash('md5').update(password, 'utf8').digest('hex');
  };

  const generateHash = () => {
    if (!password.trim()) {
      alert(t('messages.emptyPassword'));
      return;
    }
    
    let result = "";
    
    switch (hashType) {
      case "PASSWORD":
        result = mysqlPassword(password);
        break;
      case "OLD_PASSWORD":
        result = mysqlOldPassword(password);
        break;
      case "SHA1":
        result = sha1Hash(password);
        break;
      case "SHA256":
        result = sha256Hash(password);
        break;
      case "MD5":
        result = md5Hash(password);
        break;
      default:
        result = mysqlPassword(password);
    }
    
    setHashedPassword(result);
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(hashedPassword);
      alert(t('messages.copied'));
    } catch (error) {
      console.error("Copy failed:", error);
    }
  };

  const clearAll = () => {
    setPassword("");
    setHashedPassword("");
  };

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": t('meta.title'),
    "description": t('meta.description'),
    "url": `${SITE_URL}${getLocalizedHref('/tools/mysql-hash-generator')}`,
    "applicationCategory": "SecurityApplication",
    "operatingSystem": "Any",
    "permissions": "browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": locale === 'zh' ? 'CNY' : 'USD'
    },
    "creator": {
      "@type": "Organization",
      "name": tSite('name')
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container-custom py-20">
          {/* Breadcrumb */}
          <nav className="flex mb-8" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                  <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                  </svg>
                  {t('breadcrumb.home')}
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">
                    {t('breadcrumb.tools')}
                  </Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
                </div>
              </li>
            </ol>
          </nav>

          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              {t('title')}
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              {t('description')}
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            {/* Hash Type Selection */}
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg mb-8">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                {t('settings.title')}
              </h2>
              
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {[
                  { value: "PASSWORD", label: t('settings.password') },
                  { value: "OLD_PASSWORD", label: t('settings.oldPassword') },
                  { value: "SHA1", label: "SHA1" },
                  { value: "SHA256", label: "SHA256" },
                  { value: "MD5", label: "MD5" }
                ].map((type) => (
                  <label key={type.value} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="radio"
                      name="hashType"
                      value={type.value}
                      checked={hashType === type.value}
                      onChange={(e) => setHashType(e.target.value)}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">{type.label}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Input Section */}
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg mb-8">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                {t('input.title')}
              </h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('input.password')}
                  </label>
                  <textarea
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder={t('input.placeholder')}
                    className="w-full h-32 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-slate-700 dark:border-slate-600 dark:text-white dark:placeholder-gray-400 resize-none"
                  />
                </div>
                
                <div className="flex flex-wrap gap-3">
                  <button
                    onClick={generateHash}
                    className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                  >
                    {t('controls.generate')}
                  </button>
                  
                  <button
                    onClick={clearAll}
                    className="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors font-medium"
                  >
                    {t('controls.clear')}
                  </button>
                </div>
              </div>
            </div>

            {/* Output Section */}
            {hashedPassword && (
              <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg mb-8">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  {t('output.title')}
                </h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {t('output.hash')} ({hashType})
                    </label>
                    <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <code className="text-sm font-mono text-gray-900 dark:text-white break-all flex-1 mr-4">
                          {hashedPassword}
                        </code>
                        <button
                          onClick={copyToClipboard}
                          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex-shrink-0"
                        >
                          {t('controls.copy')}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Usage Examples */}
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg mb-8">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                {t('examples.title')}
              </h2>
              
              <div className="space-y-4">
                <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-2">{t('examples.createUser')}</h3>
                  <code className="text-sm text-gray-600 dark:text-gray-300 block">
                    CREATE USER 'username'@'localhost' IDENTIFIED BY PASSWORD '{hashedPassword || '*HASH_VALUE'}';
                  </code>
                </div>
                
                <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-2">{t('examples.setPassword')}</h3>
                  <code className="text-sm text-gray-600 dark:text-gray-300 block">
                    SET PASSWORD FOR 'username'@'localhost' = '{hashedPassword || '*HASH_VALUE'}';
                  </code>
                </div>
              </div>
            </div>

            {/* Security Tips */}
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                {t('tips.title')}
              </h2>
              <ul className="space-y-3 text-gray-600 dark:text-gray-300">
                <li className="flex items-start space-x-2">
                  <span className="text-yellow-500 mt-1">⚠️</span>
                  <span>{t('tips.tip1')}</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">ℹ️</span>
                  <span>{t('tips.tip2')}</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-green-500 mt-1">✓</span>
                  <span>{t('tips.tip3')}</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-red-500 mt-1">🔒</span>
                  <span>{t('tips.tip4')}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}